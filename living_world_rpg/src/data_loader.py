# File: src/data_loader.py
"""
Data Loader for Living World RPG

This module is responsible for loading all game configuration data (JSON files)
from the designated data directory. It validates file existence, handles errors,
and returns a consolidated dictionary keyed by filename (without extension).
"""

import json
import os
import logging
import time
import hashlib
from typing import Dict, Any, Optional, Set
from pathlib import Path
from utils.elastic_hash_table import ElasticHashTable

logging.basicConfig(level=logging.INFO)

class DataLoader:
    """
    Enhanced DataLoader with caching, validation, and hot-reload capabilities.

    Features:
    - File content caching with modification time tracking
    - Data validation and schema checking
    - Hot-reload support for development
    - Performance monitoring and statistics
    """

    def __init__(self, data_path: str = "data", enable_caching: bool = True, enable_validation: bool = True):
        # Determine the absolute path to the data directory.
        script_dir = os.path.dirname(os.path.abspath(__file__))
        self.data_path = os.path.join(script_dir, "..", data_path)
        self.enable_caching = enable_caching
        self.enable_validation = enable_validation

        # Caching infrastructure
        self._file_cache: Dict[str, dict] = {}  # Use regular dict for simplicity
        self._file_timestamps: Dict[str, float] = {}
        self._file_hashes: Dict[str, str] = {}

        # Validation tracking
        self._validation_errors: Dict[str, list] = {}
        self._required_fields: Dict[str, Set[str]] = self._get_required_fields()

        # Performance tracking
        self._cache_hits = 0
        self._cache_misses = 0
        self._load_times: Dict[str, float] = {}

        if not os.path.exists(self.data_path):
            logging.error(f"[DataLoader] Data directory '{self.data_path}' does not exist.")
        else:
            logging.info(f"[DataLoader] Data directory set to '{self.data_path}'. Caching: {enable_caching}, Validation: {enable_validation}")

    def load_data(self, filename: str) -> dict:
        """
        Loads a single JSON file from the data directory with caching and validation.
        Returns the file's data as a dictionary. If the file is missing or invalid, returns an empty dict.
        """
        start_time = time.time()
        filepath = os.path.join(self.data_path, filename)

        if not os.path.exists(filepath):
            logging.error(f"[DataLoader] File '{filename}' does not exist at '{filepath}'.")
            return {}

        # Check cache if enabled
        if self.enable_caching:
            cached_data = self._get_cached_data(filepath, filename)
            if cached_data is not None:
                self._cache_hits += 1
                self._load_times[filename] = time.time() - start_time
                return cached_data
            self._cache_misses += 1

        try:
            with open(filepath, "r", encoding='utf-8') as f:
                data = json.load(f)

            # Validate data if enabled
            if self.enable_validation:
                validation_errors = self._validate_data(filename, data)
                if validation_errors:
                    self._validation_errors[filename] = validation_errors
                    logging.warning(f"[DataLoader] Validation errors in '{filename}': {validation_errors}")

            # Cache the data if enabled
            if self.enable_caching:
                self._cache_data(filepath, filename, data)

            load_time = time.time() - start_time
            self._load_times[filename] = load_time
            logging.info(f"[DataLoader] Loaded data from '{filepath}' in {load_time:.3f}s.")
            return data

        except Exception as e:
            logging.error(f"[DataLoader] Error loading '{filename}': {e}")
            return {}

    def load_all_data(self) -> dict:
        """
        Loads all required JSON data files and returns a dictionary keyed by the filename (without extension).
        """
        data_files = [
            "biomes.json",
            "color_palette.json",
            "crafting_recipes.json",
            "event_thresholds.json",
            "factions.json",
            "items.json",
            "levels.json",
            "magic.json",
            "races.json",
            "seasons.json",
            "settlements.json",
            "skills.json",
            "special_conditions.json",
            "status_effects.json",
            "water.json",
            "weather.json"
        ]
        all_data = {}
        for filename in data_files:
            key = os.path.splitext(filename)[0]
            all_data[key] = self.load_data(filename)
        return all_data

    def _get_cached_data(self, filepath: str, filename: str) -> Optional[dict]:
        """Check if cached data is still valid and return it."""
        try:
            current_mtime = os.path.getmtime(filepath)
            cached_mtime = self._file_timestamps.get(filepath)

            # Check if file has been modified
            if cached_mtime and current_mtime <= cached_mtime:
                cached_data = self._file_cache.get(filepath)
                if cached_data is not None:
                    return cached_data

            return None
        except OSError:
            return None

    def _cache_data(self, filepath: str, filename: str, data: dict) -> None:
        """Cache the loaded data with timestamp tracking."""
        try:
            # Use regular dict assignment
            self._file_cache[filepath] = data
            self._file_timestamps[filepath] = os.path.getmtime(filepath)

            # Generate hash for integrity checking
            data_str = json.dumps(data, sort_keys=True)
            self._file_hashes[filepath] = hashlib.md5(data_str.encode()).hexdigest()
        except Exception as e:
            logging.warning(f"[DataLoader] Failed to cache data for '{filename}': {e}")

    def _validate_data(self, filename: str, data: dict) -> list:
        """Validate loaded data against expected schema."""
        errors = []
        file_key = os.path.splitext(filename)[0]

        # Check required fields
        required_fields = self._required_fields.get(file_key, set())
        for field in required_fields:
            if field not in data:
                errors.append(f"Missing required field: {field}")

        # Specific validation rules
        if file_key == "biomes":
            errors.extend(self._validate_biomes(data))
        elif file_key == "items":
            errors.extend(self._validate_items(data))
        elif file_key == "crafting_recipes":
            errors.extend(self._validate_recipes(data))

        return errors

    def _get_required_fields(self) -> Dict[str, Set[str]]:
        """Define required fields for each data file."""
        return {
            "biomes": {"Molten Fields", "Twilight Marsh", "Golden Sands Desert"},
            "items": set(),  # Items can be dynamic
            "crafting_recipes": set(),
            "factions": set(),
            "color_palette": {"biomes", "fallbacks"}
        }

    def _validate_biomes(self, data: dict) -> list:
        """Validate biome data structure."""
        errors = []
        for biome_name, biome_data in data.items():
            if not isinstance(biome_data, dict):
                errors.append(f"Biome '{biome_name}' must be a dictionary")
                continue

            # Check for required biome fields
            required = ["description", "thresholds"]
            for field in required:
                if field not in biome_data:
                    errors.append(f"Biome '{biome_name}' missing field: {field}")

        return errors

    def _validate_items(self, data: dict) -> list:
        """Validate item data structure."""
        errors = []
        for item_name, item_data in data.items():
            if not isinstance(item_data, dict):
                errors.append(f"Item '{item_name}' must be a dictionary")
                continue

            # Check for common item fields
            if "type" not in item_data:
                errors.append(f"Item '{item_name}' missing type field")

        return errors

    def _validate_recipes(self, data: dict) -> list:
        """Validate crafting recipe data structure."""
        errors = []
        for recipe_name, recipe_data in data.items():
            if not isinstance(recipe_data, dict):
                errors.append(f"Recipe '{recipe_name}' must be a dictionary")
                continue

            # Check for required recipe fields
            required = ["ingredients", "result"]
            for field in required:
                if field not in recipe_data:
                    errors.append(f"Recipe '{recipe_name}' missing field: {field}")

        return errors

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics for the data loader."""
        total_requests = self._cache_hits + self._cache_misses
        hit_rate = (self._cache_hits / total_requests * 100) if total_requests > 0 else 0

        return {
            'cache_hits': self._cache_hits,
            'cache_misses': self._cache_misses,
            'hit_rate': hit_rate,
            'cached_files': len(self._file_cache),
            'validation_errors': len(self._validation_errors),
            'average_load_time': sum(self._load_times.values()) / len(self._load_times) if self._load_times else 0,
            'total_files_loaded': len(self._load_times)
        }

    def clear_cache(self) -> None:
        """Clear all cached data."""
        self._file_cache.clear()
        self._file_timestamps.clear()
        self._file_hashes.clear()
        logging.info("[DataLoader] Cache cleared.")

    def reload_file(self, filename: str) -> dict:
        """Force reload a specific file, bypassing cache."""
        filepath = os.path.join(self.data_path, filename)

        # Remove from cache
        if filepath in self._file_cache:
            del self._file_cache[filepath]
        if filepath in self._file_timestamps:
            del self._file_timestamps[filepath]
        if filepath in self._file_hashes:
            del self._file_hashes[filepath]

        # Reload the file
        return self.load_data(filename)

    def get_validation_errors(self) -> Dict[str, list]:
        """Get all validation errors found during loading."""
        return self._validation_errors.copy()

    def has_file_changed(self, filename: str) -> bool:
        """Check if a file has been modified since last load."""
        filepath = os.path.join(self.data_path, filename)
        if not os.path.exists(filepath):
            return False

        try:
            current_mtime = os.path.getmtime(filepath)
            cached_mtime = self._file_timestamps.get(filepath)
            return cached_mtime is None or current_mtime > cached_mtime
        except OSError:
            return True


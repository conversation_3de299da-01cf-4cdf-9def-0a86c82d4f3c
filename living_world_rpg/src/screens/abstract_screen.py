from abc import ABC, abstractmethod
from typing import Optional
import pygame

from core.game_state import GameState
from ui_util import draw_button, draw_text

class AbstractScreen(ABC):
    """Abstract base class for all game screens. Provides unified UI helpers via ui_util."""

    def __init__(self, gs: GameState) -> None:
        """Initialize the screen with the game state."""
        self.gs = gs
        self.font = pygame.font.Font(None, 30)
        self.small_font = pygame.font.Font(None, 24)

    @abstractmethod
    def handle_events(self) -> None:
        """Handle events for this screen."""
        pass

    @abstractmethod
    def update(self, dt: float) -> None:
        """Update the screen state."""
        pass

    @abstractmethod
    def render(self, screen: pygame.Surface) -> None:
        """Render the screen content."""
        pass

    # UI helpers now delegate to ui_util for consistency
    def ui_draw_button(self, screen, text, x, y, width=None, height=None, color=(0,150,0), hover_color=(0,200,0)):
        return draw_button(screen, self.font, text, x, y, width, height, color, hover_color)

    def ui_draw_text(self, screen, text, x, y, color=(255,255,255)):
        draw_text(screen, self.font, text, x, y, color)

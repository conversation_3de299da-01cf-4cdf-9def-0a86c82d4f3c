"""
Enhanced CraftingScreen for Living World RPG
Allows players to view recipes and craft items with improved inventory display,
case-insensitive item matching, real-time updates, and comprehensive error handling.
"""
import pygame
import logging
from typing import Dict, List, Optional, Tuple, Any
from screens.abstract_screen import AbstractScreen
from ui_util import draw_button, draw_text
from controls import get_control_from_event
from core.components import InventoryComponent

class CraftingScreen(AbstractScreen):
    def __init__(self, gs, ecs_world=None, data=None):
        self.gs = gs
        self.ecs_world = ecs_world
        self.data = data

        # Fallback to main import if dependencies not provided (for backward compatibility)
        if not self.data or not self.ecs_world:
            import main
            self.data = main.data
            self.ecs_world = main.ecs_world

        self.recipes = self.data.get("crafting_recipes", {}).get("recipes", [])
        self.scroll_offset = 0
        self.recipe_button_rects = []
        self.craft_button_rect = None
        self.back_button_rect = None
        self.hovered_index = None
        self.font = pygame.font.Font(None, 30)
        self.small_font = pygame.font.Font(None, 24)

        # Item categories and filtering
        self.categories = ["All", "Weapons", "Tools", "Armor", "Potions", "Resources", "Food"]
        self.selected_category = "All"
        self.category_buttons = []
        self.filtered_recipes = self.recipes.copy()

        # Load item icons for recipes
        import os
        asset_dir = os.path.normpath(os.path.join(os.path.dirname(__file__), '..', 'assets'))
        self.item_icons = {}
        self.items_data = self.data.get("items", {})

        # Load all item icons
        for item_name in self.items_data:
            img_path = os.path.join(asset_dir, f"{item_name}.png")
            if os.path.exists(img_path):
                try:
                    img = pygame.image.load(img_path).convert_alpha()
                    self.item_icons[item_name] = pygame.transform.scale(img, (32, 32))
                except Exception:
                    pass

        # Create default icon for items without images
        self.default_icon = pygame.Surface((32, 32), pygame.SRCALPHA)
        pygame.draw.rect(self.default_icon, (100, 100, 100), (0, 0, 32, 32))
        pygame.draw.rect(self.default_icon, (200, 200, 200), (0, 0, 32, 32), 1)

        # Apply initial filtering
        self._apply_category_filter()

        # Enhanced inventory tracking
        self._last_inventory_state = {}
        self._inventory_changed = True

        # Error tracking for debugging
        self._item_lookup_errors = {}
        self._missing_items = set()

    def _get_normalized_item_name(self, item_name: str) -> str:
        """Get normalized item name for consistent lookups."""
        if not item_name:
            return ""
        return str(item_name).lower().strip().replace('_', ' ')

    def _find_item_in_inventory(self, inv_comp: InventoryComponent, target_item: str) -> Tuple[str, int]:
        """
        Find an item in inventory using enhanced case-insensitive matching.

        Args:
            inv_comp: The inventory component to search
            target_item: The item name to find

        Returns:
            Tuple of (actual_item_name, quantity) or ("", 0) if not found
        """
        if not inv_comp or not target_item:
            return ("", 0)

        try:
            # First try direct lookup using InventoryComponent's built-in method
            count = inv_comp.get_item_count(target_item)
            if count > 0:
                # Find the actual name in inventory
                actual_name = inv_comp._get_actual_name(target_item)
                return (actual_name or target_item, count)

            # If not found, try various name variations
            normalized_target = self._get_normalized_item_name(target_item)

            for item_name, quantity in inv_comp.items.items():
                if quantity > 0:
                    normalized_item = self._get_normalized_item_name(item_name)
                    if normalized_item == normalized_target:
                        return (item_name, quantity)

            # Try partial matching as last resort
            for item_name, quantity in inv_comp.items.items():
                if quantity > 0:
                    normalized_item = self._get_normalized_item_name(item_name)
                    if normalized_target in normalized_item or normalized_item in normalized_target:
                        logging.debug(f"[CraftingScreen] Partial match: '{target_item}' -> '{item_name}'")
                        return (item_name, quantity)

            return ("", 0)

        except Exception as e:
            logging.error(f"[CraftingScreen] Error finding item '{target_item}': {e}")
            self._item_lookup_errors[target_item] = str(e)
            return ("", 0)

    def _check_inventory_changes(self, inv_comp: InventoryComponent) -> bool:
        """Check if inventory has changed since last check."""
        if not inv_comp:
            return False

        current_state = dict(inv_comp.items.items())

        if current_state != self._last_inventory_state:
            self._last_inventory_state = current_state.copy()
            self._inventory_changed = True
            return True

        return False

    def _apply_category_filter(self):
        """Filter recipes based on the selected category."""
        if self.selected_category == "All":
            self.filtered_recipes = self.recipes.copy()
            return

        # Filter recipes based on output item type
        self.filtered_recipes = []
        for recipe in self.recipes:
            output_item = recipe.get("output", "")
            item_data = self.items_data.get(output_item, {})
            item_type = item_data.get("type", "").lower()

            # Map item types to categories
            category_map = {
                "weapon": "Weapons",
                "tool": "Tools",
                "armor": "Armor",
                "potion": "Potions",
                "resource": "Resources",
                "food": "Food"
            }

            if category_map.get(item_type) == self.selected_category:
                self.filtered_recipes.append(recipe)

        # Reset scroll offset when changing categories
        self.scroll_offset = 0

    def handle_events(self):
        for event in pygame.event.get():
            if event.type == pygame.KEYDOWN:
                if event.key in (pygame.K_ESCAPE, pygame.K_c):
                    self.gs.current_state = "gameplay"
                elif event.key == pygame.K_UP:
                    self.scroll_offset = max(0, self.scroll_offset - 1)
                elif event.key == pygame.K_DOWN:
                    self.scroll_offset = min(len(self.filtered_recipes) - 1, self.scroll_offset + 1)
                elif event.key == pygame.K_RETURN:
                    if self.filtered_recipes:
                        recipe = self.filtered_recipes[self.scroll_offset]
                        if self.try_craft(recipe):
                            self.gs.show_feedback_message(f"Crafted {recipe['name']}!", color=(0,255,0))
                        else:
                            self.gs.show_feedback_message("Missing ingredients!", color=(255,0,0))
            elif event.type == pygame.MOUSEMOTION:
                pos = event.pos
                self.hovered_index = None
                for idx, rect in enumerate(self.recipe_button_rects):
                    if rect.collidepoint(pos):
                        self.hovered_index = idx
                        break
            elif event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:
                pos = event.pos

                # Check category buttons
                for idx, (rect, category) in enumerate(self.category_buttons):
                    if rect.collidepoint(pos):
                        self.selected_category = category
                        self._apply_category_filter()
                        break

                # Select recipe
                for idx, rect in enumerate(self.recipe_button_rects):
                    if rect.collidepoint(pos):
                        self.scroll_offset = idx
                        break
                # Craft
                if self.craft_button_rect and self.craft_button_rect.collidepoint(pos):
                    if self.filtered_recipes:
                        recipe = self.filtered_recipes[self.scroll_offset]
                        if self.try_craft(recipe):
                            self.gs.show_feedback_message(f"Crafted {recipe['name']}!", color=(0,255,0))
                        else:
                            self.gs.show_feedback_message("Missing ingredients!", color=(255,0,0))
                # Back
                if self.back_button_rect and self.back_button_rect.collidepoint(pos):
                    self.gs.current_state = "gameplay"

    def try_craft(self, recipe: Dict[str, Any]) -> bool:
        """
        Enhanced crafting logic with better error handling and logging.

        Args:
            recipe: The recipe dictionary to craft

        Returns:
            True if crafting succeeded, False otherwise
        """
        try:
            player = self.ecs_world.get_player_entity()
            if not player:
                logging.warning("[CraftingScreen] No player entity found")
                return False

            inv_comp = player.get_component(InventoryComponent)
            if not inv_comp:
                logging.warning("[CraftingScreen] Player has no inventory component")
                return False

            recipe_name = recipe.get("name", "Unknown Recipe")
            ingredients = recipe.get("ingredients", {})

            # Enhanced ingredient checking with detailed logging
            missing_ingredients = []
            for ingredient_name, required_count in ingredients.items():
                actual_name, available_count = self._find_item_in_inventory(inv_comp, ingredient_name)

                if available_count < required_count:
                    missing_ingredients.append({
                        'name': ingredient_name,
                        'required': required_count,
                        'available': available_count,
                        'actual_name': actual_name
                    })

            if missing_ingredients:
                # Log detailed information about missing ingredients
                for missing in missing_ingredients:
                    logging.debug(f"[CraftingScreen] Missing ingredient for '{recipe_name}': "
                                f"need {missing['required']} {missing['name']}, "
                                f"have {missing['available']} {missing['actual_name']}")
                return False

            # Deduct ingredients using enhanced lookup
            for ingredient_name, required_count in ingredients.items():
                actual_name, available_count = self._find_item_in_inventory(inv_comp, ingredient_name)
                if actual_name:
                    inv_comp.remove_item(actual_name, required_count)
                    logging.debug(f"[CraftingScreen] Removed {required_count} {actual_name} for '{recipe_name}'")

            # Add crafted item(s)
            output_item = recipe.get("output")
            output_qty = recipe.get("output_qty", 1)

            if output_item:
                inv_comp.add_item(output_item, output_qty)
                logging.info(f"[CraftingScreen] Crafted {output_qty} {output_item} from '{recipe_name}'")

            # Mark inventory as changed for UI updates
            self._inventory_changed = True

            return True

        except Exception as e:
            logging.error(f"[CraftingScreen] Error crafting '{recipe.get('name', 'Unknown')}': {e}")
            return False

    def update(self, dt: float) -> None:
        """
        Enhanced update method with real-time inventory monitoring.

        Args:
            dt: Delta time since last update
        """
        try:
            # Check for inventory changes for real-time updates
            player = self.ecs_world.get_player_entity()

            if player:
                inv_comp = player.get_component(InventoryComponent)
                if inv_comp:
                    # Check if inventory has changed
                    if self._check_inventory_changes(inv_comp):
                        logging.debug("[CraftingScreen] Inventory changed, updating display")

                        # Clear error tracking when inventory changes
                        self._item_lookup_errors.clear()
                        self._missing_items.clear()

                        # Re-apply category filter to update recipe availability
                        self._apply_category_filter()

        except Exception as e:
            logging.error(f"[CraftingScreen] Error in update: {e}")

    def render(self, screen):
        screen.fill((30,30,30))
        self.ui_draw_text(screen, "Crafting", 40, 40, color=(255,255,0))

        # Get player inventory
        player = self.ecs_world.get_player_entity()
        inv_comp = None
        if player:
            inv_comp = player.get_component(InventoryComponent)

        # Draw category filter buttons
        self.category_buttons = []
        category_x = 40
        category_y = 80
        for category in self.categories:
            # Highlight selected category
            is_selected = category == self.selected_category
            button_color = (100, 100, 150) if is_selected else (70, 70, 100)
            hover_color = (120, 120, 170) if is_selected else (90, 90, 120)

            # Draw category button
            _, rect = self.ui_draw_button(
                screen, category, category_x, category_y, 80, 30,
                button_color, hover_color
            )
            self.category_buttons.append((rect, category))
            category_x += 90

            # Draw selection indicator
            if is_selected:
                pygame.draw.rect(screen, (200, 200, 255), rect, 2)

        # Draw recipe list on left
        y = 130  # Start recipes below category buttons
        self.recipe_button_rects = []

        if not self.filtered_recipes:
            self.ui_draw_text(screen, "No recipes in this category", 40, y + 20, color=(200, 200, 200))

        for i, recipe in enumerate(self.filtered_recipes):
            # Enhanced craftability checking with detailed ingredient analysis
            can_craft = False
            missing_count = 0
            total_ingredients = len(recipe.get("ingredients", {}))

            if inv_comp:
                can_craft = True
                for ingredient_name, required_count in recipe.get("ingredients", {}).items():
                    actual_name, available_count = self._find_item_in_inventory(inv_comp, ingredient_name)
                    if available_count < required_count:
                        can_craft = False
                        missing_count += 1

            # Enhanced button colors based on craftability and missing ingredients
            if can_craft:
                button_color = (80, 120, 80)  # Green for craftable
                hover_color = (120, 160, 120)
            elif missing_count == total_ingredients:
                button_color = (80, 80, 80)   # Dark gray for no ingredients
                hover_color = (120, 120, 120)
            else:
                button_color = (120, 80, 80)  # Red for partially missing
                hover_color = (160, 120, 120)

            # Draw recipe button with enhanced visual feedback
            recipe_name = recipe.get("name", "Unknown Recipe")
            if missing_count > 0 and total_ingredients > 0:
                recipe_name += f" ({total_ingredients - missing_count}/{total_ingredients})"

            _, rect = self.ui_draw_button(
                screen, recipe_name, 40, y, 200, 40,
                button_color, hover_color if i == self.hovered_index else button_color
            )

            # Enhanced selection indicator
            if i == self.scroll_offset:
                pygame.draw.rect(screen, (0, 255, 0), rect, 3)
                # Add glow effect for selected recipe
                glow_rect = pygame.Rect(rect.x - 2, rect.y - 2, rect.width + 4, rect.height + 4)
                pygame.draw.rect(screen, (0, 200, 0), glow_rect, 1)

            self.recipe_button_rects.append(rect)
            y += 50

        # Draw details for selected recipe on right
        x0, y0 = 300, 100
        if self.filtered_recipes and self.scroll_offset < len(self.filtered_recipes):
            recipe = self.filtered_recipes[self.scroll_offset]

            # Draw recipe name and description
            recipe_name = recipe.get("name", "Unknown Recipe")
            self.ui_draw_text(screen, recipe_name, x0, y0, color=(255,255,0))

            desc = recipe.get("description", "No description.")
            self.ui_draw_text(screen, desc, x0, y0 + 30, color=(255,255,255))

            # Draw output item info
            output_item = recipe.get("output", "")
            output_qty = recipe.get("output_qty", 1)

            y_output = y0 + 70
            self.ui_draw_text(screen, "Creates:", x0, y_output, color=(255,255,0))

            # Get item data and icon
            item_data = self.items_data.get(output_item, {})
            icon = self.item_icons.get(output_item, self.default_icon)

            # Draw output item with icon
            screen.blit(icon, (x0, y_output + 30))
            item_name = output_item.replace('_', ' ').title()
            self.ui_draw_text(screen, f"{item_name} x{output_qty}",
                     x0 + icon.get_width() + 10, y_output + 30 + (icon.get_height()//2),
                     color=(255,255,255))

            # Draw item type and rarity if available
            item_type = item_data.get("type", "")
            item_rarity = item_data.get("rarity", "")
            if item_type or item_rarity:
                type_text = f"Type: {item_type.capitalize()}" if item_type else ""
                rarity_text = f"Rarity: {item_rarity.capitalize()}" if item_rarity else ""
                combined_text = f"{type_text}  {rarity_text}".strip()
                self.ui_draw_text(screen, combined_text,
                         x0, y_output + 70, color=(200,200,200))

            # Draw ingredients section
            y1 = y_output + 110
            self.ui_draw_text(screen, "Required Ingredients:", x0, y1, color=(255,255,0))

            for idx, (ingredient_name, required_count) in enumerate(recipe.get("ingredients", {}).items()):
                # Enhanced item lookup with detailed information
                actual_name, actual_count = self._find_item_in_inventory(inv_comp, ingredient_name)

                # Get item data with fallback
                item_data = self.items_data.get(ingredient_name, {})
                if not item_data and actual_name:
                    item_data = self.items_data.get(actual_name, {})

                # Enhanced icon finding with better fallback
                icon = None
                icon_search_names = [ingredient_name, actual_name] if actual_name else [ingredient_name]

                for search_name in icon_search_names:
                    if search_name:
                        normalized_name = self._get_normalized_item_name(search_name)
                        for icon_name in self.item_icons:
                            if self._get_normalized_item_name(icon_name) == normalized_name:
                                icon = self.item_icons[icon_name]
                                break
                        if icon:
                            break

                if not icon:
                    icon = self.default_icon
                    # Track missing icons for debugging
                    self._missing_items.add(ingredient_name)

                y_offset = y1 + (idx+1) * 40
                screen.blit(icon, (x0, y_offset))

                # Enhanced item name formatting
                display_name = actual_name if actual_name else ingredient_name
                formatted_name = display_name.replace('_', ' ').title()

                # Enhanced color coding based on availability
                if actual_count >= required_count:
                    text_color = (100, 255, 100)  # Bright green for sufficient
                elif actual_count > 0:
                    text_color = (255, 200, 100)  # Orange for partial
                else:
                    text_color = (255, 100, 100)  # Red for missing

                # Enhanced text with better information
                item_text = f"{formatted_name}: {actual_count}/{required_count}"
                if actual_name and actual_name != ingredient_name:
                    item_text += f" (as {actual_name})"

                # Draw text with enhanced positioning
                text_x = x0 + icon.get_width() + 10
                text_y = y_offset + (icon.get_height()//2)
                self.ui_draw_text(screen, item_text, text_x, text_y, color=text_color)

                # Add progress bar for ingredient availability
                if required_count > 0:
                    bar_width = 100
                    bar_height = 4
                    bar_x = text_x
                    bar_y = text_y + 20

                    # Background bar
                    pygame.draw.rect(screen, (60, 60, 60), (bar_x, bar_y, bar_width, bar_height))

                    # Progress bar
                    progress = min(1.0, actual_count / required_count)
                    progress_width = int(bar_width * progress)
                    if progress_width > 0:
                        bar_color = (100, 255, 100) if progress >= 1.0 else (255, 200, 100)
                        pygame.draw.rect(screen, bar_color, (bar_x, bar_y, progress_width, bar_height))

        # Draw player inventory section
        if inv_comp:
            inv_x = 600
            inv_y = 100
            self.ui_draw_text(screen, "Your Inventory:", inv_x, inv_y, color=(255,255,0))

            # Draw scrollable inventory grid
            items_per_row = 3
            item_size = 40
            padding = 10
            max_items_visible = 15  # Show 5 rows of 3 items

            # Convert inventory to list for easier display
            inventory_items = list(inv_comp.items.items())

            # Track hovered item for tooltip
            hovered_item_info = None
            mouse_pos = pygame.mouse.get_pos()

            for i in range(min(max_items_visible, len(inventory_items))):
                item_name, qty = inventory_items[i]

                row = i // items_per_row
                col = i % items_per_row

                x = inv_x + col * (item_size + padding)
                y = inv_y + 40 + row * (item_size + padding)

                # Create item rect for hover detection
                item_rect = pygame.Rect(x, y, item_size, item_size)

                # Check if mouse is hovering over this item
                if item_rect.collidepoint(mouse_pos):
                    # Get item data for tooltip
                    item_data = None
                    if item_name is not None:
                        normalized_name = str(item_name).lower()
                        for data_name in self.items_data:
                            if data_name.lower() == normalized_name:
                                item_data = self.items_data[data_name]
                                break

                        # Log missing item data for debugging
                        if not item_data:
                            import logging
                            logging.debug(f"Missing item data for: {item_name}")

                    if item_data:
                        hovered_item_info = {
                            "name": item_name.replace('_', ' ').title(),
                            "description": item_data.get("description", "No description available."),
                            "type": item_data.get("type", ""),
                            "rarity": item_data.get("rarity", ""),
                            "quantity": qty
                        }
                    else:
                        hovered_item_info = {
                            "name": item_name.replace('_', ' ').title(),
                            "description": "No description available.",
                            "quantity": qty
                        }

                # Draw item background with highlight if hovered
                bg_color = (80,80,80) if item_rect.collidepoint(mouse_pos) else (60,60,60)
                pygame.draw.rect(screen, bg_color, item_rect)
                pygame.draw.rect(screen, (100,100,100), item_rect, 1)  # Border

                # Draw item icon if available
                icon = None
                # Try to find icon with different case variations
                if item_name is not None:
                    normalized_name = str(item_name).lower()
                    for icon_name in self.item_icons:
                        if icon_name.lower() == normalized_name:
                            icon = self.item_icons[icon_name]
                            break

                if not icon:
                    icon = self.default_icon

                # Log missing icons for debugging
                if item_name is not None and icon is self.default_icon:
                    import logging
                    logging.debug(f"Missing icon for inventory item: {item_name}")

                icon_scaled = pygame.transform.scale(icon, (item_size-4, item_size-4))
                screen.blit(icon_scaled, (x+2, y+2))

                # Draw quantity
                qty_text = f"x{qty}"
                self.ui_draw_text(screen, qty_text,
                         x + item_size - 15, y + item_size - 15, color=(255,255,255))

            # Draw tooltip for hovered item
            if hovered_item_info:
                tooltip_width = 200
                tooltip_x = min(mouse_pos[0] + 20, screen.get_width() - tooltip_width - 10)
                tooltip_y = mouse_pos[1] + 20

                # Background
                tooltip_height = 80
                if "type" in hovered_item_info and "rarity" in hovered_item_info:
                    tooltip_height += 20

                tooltip_rect = pygame.Rect(tooltip_x, tooltip_y, tooltip_width, tooltip_height)
                pygame.draw.rect(screen, (40, 40, 40), tooltip_rect)
                pygame.draw.rect(screen, (100, 100, 100), tooltip_rect, 1)

                # Item name
                self.ui_draw_text(screen, hovered_item_info["name"],
                         tooltip_x + 10, tooltip_y + 10, color=(255, 255, 0))

                # Quantity
                self.ui_draw_text(screen, f"Quantity: {hovered_item_info['quantity']}",
                         tooltip_x + 10, tooltip_y + 30, color=(200, 200, 200))

                # Type and rarity if available
                if "type" in hovered_item_info and "rarity" in hovered_item_info:
                    type_text = f"Type: {hovered_item_info['type'].capitalize()}" if hovered_item_info["type"] else ""
                    rarity_text = f"Rarity: {hovered_item_info['rarity'].capitalize()}" if hovered_item_info["rarity"] else ""
                    combined_text = f"{type_text}  {rarity_text}".strip()
                    self.ui_draw_text(screen, combined_text,
                             tooltip_x + 10, tooltip_y + 50, color=(200, 200, 200))

                    # Description
                    description = hovered_item_info["description"]
                    # Truncate if too long
                    if len(description) > 30:
                        description = description[:27] + "..."
                    self.ui_draw_text(screen, description,
                             tooltip_x + 10, tooltip_y + 70, color=(255, 255, 255))
                else:
                    # Description
                    description = hovered_item_info["description"]
                    # Truncate if too long
                    if len(description) > 30:
                        description = description[:27] + "..."
                    self.ui_draw_text(screen, description,
                             tooltip_x + 10, tooltip_y + 50, color=(255, 255, 255))

        # Draw Craft and Back buttons
        # Determine if current recipe can be crafted
        can_craft = False
        if self.filtered_recipes and self.scroll_offset < len(self.filtered_recipes) and inv_comp:
            recipe = self.filtered_recipes[self.scroll_offset]
            can_craft = True
            for name, count in recipe.get("ingredients", {}).items():
                if inv_comp.get_item_count(name) < count:
                    can_craft = False
                    break

        craft_color = (70,150,70) if can_craft else (70,70,70)
        craft_hover = (100,180,100) if can_craft else (100,100,100)

        # Draw recipe count and enhanced status information
        recipe_count_text = f"Showing {len(self.filtered_recipes)} of {len(self.recipes)} recipes"
        self.ui_draw_text(screen, recipe_count_text, 800, 70, color=(200, 200, 200))

        # Draw debug information if there are issues
        debug_y = 200
        if self._item_lookup_errors:
            self.ui_draw_text(screen, f"Lookup errors: {len(self._item_lookup_errors)}",
                     800, debug_y, color=(255, 200, 100))
            debug_y += 20

        if self._missing_items:
            self.ui_draw_text(screen, f"Missing icons: {len(self._missing_items)}",
                     800, debug_y, color=(255, 200, 100))
            debug_y += 20

        # Enhanced craft button with better feedback
        craft_text = "Craft"
        if self.filtered_recipes and self.scroll_offset < len(self.filtered_recipes):
            selected_recipe = self.filtered_recipes[self.scroll_offset]
            if inv_comp:
                missing_ingredients = []
                for ingredient_name, required_count in selected_recipe.get("ingredients", {}).items():
                    _, available_count = self._find_item_in_inventory(inv_comp, ingredient_name)
                    if available_count < required_count:
                        missing_ingredients.append(ingredient_name)

                if missing_ingredients:
                    craft_text = f"Missing {len(missing_ingredients)} items"

        _, self.craft_button_rect = draw_button(screen, self.font, craft_text, 800, 100, 120, 40,
                                               craft_color, craft_hover)
        _, self.back_button_rect = draw_button(screen, self.font, "Back", 800, 160, 120, 40,
                                              (70,70,70), (100,100,100))

        # Draw inventory status
        if inv_comp:
            total_items = len(inv_comp.items)
            total_quantity = sum(inv_comp.items.values())
            inventory_text = f"Inventory: {total_items} types, {total_quantity} total"
            self.ui_draw_text(screen, inventory_text, 40, screen.get_height() - 30,
                     color=(200, 200, 200))

        # Only flip the display if we're not in a test environment
        try:
            pygame.display.flip()
        except pygame.error:
            # Skip display flip during testing
            pass

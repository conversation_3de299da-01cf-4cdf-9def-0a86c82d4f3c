"""
World Renderer for Living World RPG

Handles rendering of the game world with different levels of detail (LOD)
and view frustum culling for optimal performance.
"""

import pygame
import numpy as np
from typing import Tuple, List, Dict, Optional
from dataclasses import dataclass
import logging
from .chunk_manager import ChunkManager, ChunkData

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class RenderSettings:
    """Configuration for world rendering."""
    tile_size: int = 32
    lod0_threshold: float = 1.0  # Zoom level for LOD 0 (highest detail)
    lod1_threshold: float = 0.5  # Zoom level for LOD 1 (medium detail)
    lod2_threshold: float = 0.25  # Zoom level for LOD 2 (lowest detail)
    view_padding: int = 2  # Number of chunks to render outside the view

class WorldRenderer:
    """Handles rendering of the game world with LOD support."""
    
    def __init__(self, chunk_manager: ChunkManager, settings: Optional[RenderSettings] = None):
        """
        Initialize the WorldRenderer.
        
        Args:
            chunk_manager: The ChunkManager instance to use
            settings: Optional render settings
        """
        self.chunk_manager = chunk_manager
        self.settings = settings or RenderSettings()
        self.camera_pos = (0, 0)  # World coordinates of the camera
        self.zoom_level = 1.0
        self.view_size = (800, 600)  # Default view size, will be updated
        
        # Cache for rendered chunks
        self.chunk_surfaces: Dict[Tuple[int, int, int], pygame.Surface] = {}
    
    def set_view_size(self, width: int, height: int) -> None:
        """Update the viewport size."""
        self.view_size = (width, height)
    
    def set_camera_position(self, x: float, y: float) -> None:
        """Set the camera position in world coordinates."""
        self.camera_pos = (x, y)
    
    def set_zoom(self, zoom: float) -> None:
        """Set the zoom level (1.0 = normal, >1.0 = zoom in, <1.0 = zoom out)."""
        self.zoom_level = max(0.1, min(5.0, zoom))  # Clamp zoom level
    
    def get_lod_level(self) -> int:
        """Determine the appropriate LOD level based on zoom."""
        if self.zoom_level >= self.settings.lod0_threshold:
            return 0  # Highest detail
        elif self.zoom_level >= self.settings.lod1_threshold:
            return 1  # Medium detail
        else:
            return 2  # Lowest detail
    
    def world_to_screen(self, world_x: float, world_y: float) -> Tuple[float, float]:
        """Convert world coordinates to screen coordinates."""
        screen_x = (world_x - self.camera_pos[0]) * self.zoom_level + self.view_size[0] / 2
        screen_y = (world_y - self.camera_pos[1]) * self.zoom_level + self.view_size[1] / 2
        return screen_x, screen_y
    
    def screen_to_world(self, screen_x: float, screen_y: float) -> Tuple[float, float]:
        """Convert screen coordinates to world coordinates."""
        world_x = (screen_x - self.view_size[0] / 2) / self.zoom_level + self.camera_pos[0]
        world_y = (screen_y - self.view_size[1] / 2) / self.zoom_level + self.camera_pos[1]
        return world_x, world_y
    
    def get_view_rect(self) -> Tuple[float, float, float, float]:
        """Get the world-space rectangle of the current view."""
        half_width = self.view_size[0] / (2 * self.zoom_level)
        half_height = self.view_size[1] / (2 * self.zoom_level)
        return (
            self.camera_pos[0] - half_width,
            self.camera_pos[1] - half_height,
            half_width * 2,
            half_height * 2
        )
    
    def render(self, surface: pygame.Surface) -> None:
        """Render the world to the given surface."""
        # Clear the surface
        surface.fill((0, 0, 0))
        
        # Get visible chunks based on current view
        view_rect = self.get_view_rect()
        lod_level = self.get_lod_level()
        visible_chunks = self.chunk_manager.get_visible_chunks(view_rect, lod_level)
        
        # Render each visible chunk
        for chunk_x, chunk_y, chunk_data in visible_chunks:
            self._render_chunk(surface, chunk_x, chunk_y, chunk_data, lod_level)
        
        # Optional: Draw debug info
        self._draw_debug_info(surface, len(visible_chunks), lod_level)
    
    def _render_chunk(self, surface: pygame.Surface, chunk_x: int, chunk_y: int, 
                     chunk_data: np.ndarray, lod_level: int) -> None:
        """Render a single chunk."""
        chunk_key = (chunk_x, chunk_y, lod_level)
        
        # Check if we have a cached surface for this chunk and LOD
        if chunk_key in self.chunk_surfaces:
            chunk_surface = self.chunk_surfaces[chunk_key]
        else:
            # Create a new surface for this chunk and LOD
            chunk_size = chunk_data.shape[0]  # Assuming square chunks
            chunk_surface = pygame.Surface((chunk_size, chunk_size), pygame.SRCALPHA)
            
            # Convert the chunk data to a surface
            # This is a simplified example - you'll need to map your biome/terrain data to colors
            for y in range(chunk_size):
                for x in range(chunk_size):
                    # Map your biome/terrain data to colors here
                    # This is just a placeholder - replace with your actual rendering logic
                    if chunk_data[y, x] == 0:  # Example: water
                        color = (0, 0, 255, 255)
                    elif chunk_data[y, x] == 1:  # Example: grass
                        color = (0, 255, 0, 255)
                    else:  # Example: other terrain
                        color = (128, 128, 128, 255)
                    
                    chunk_surface.set_at((x, y), color)
            
            # Cache the surface for future use
            self.chunk_surfaces[chunk_key] = chunk_surface
        
        # Calculate the chunk's position on screen
        chunk_world_x = chunk_x * self.chunk_manager.chunk_size
        chunk_world_y = chunk_y * self.chunk_manager.chunk_size
        screen_x, screen_y = self.world_to_screen(chunk_world_x, chunk_world_y)
        
        # Scale the chunk surface based on zoom
        scaled_size = (
            int(self.chunk_manager.chunk_size * self.zoom_level),
            int(self.chunk_manager.chunk_size * self.zoom_level)
        )
        
        # Only render if the chunk is visible on screen
        if (screen_x + scaled_size[0] >= 0 and screen_x < self.view_size[0] and
            screen_y + scaled_size[1] >= 0 and screen_y < self.view_size[1]):
            
            # Scale the chunk surface if needed
            if self.zoom_level != 1.0:
                scaled_surface = pygame.transform.scale(chunk_surface, scaled_size)
                surface.blit(scaled_surface, (int(screen_x), int(screen_y)))
            else:
                surface.blit(chunk_surface, (int(screen_x), int(screen_y)))
    
    def _draw_debug_info(self, surface: pygame.Surface, visible_chunks: int, lod_level: int) -> None:
        """Draw debug information on the screen."""
        font = pygame.font.Font(None, 24)
        
        # Draw FPS counter
        fps = int(pygame.time.Clock().get_fps())
        fps_text = font.render(f"FPS: {fps}", True, (255, 255, 255))
        surface.blit(fps_text, (10, 10))
        
        # Draw visible chunks counter
        chunks_text = font.render(f"Chunks: {visible_chunks}", True, (255, 255, 255))
        surface.blit(chunks_text, (10, 40))
        
        # Draw LOD level
        lod_text = font.render(f"LOD: {lod_level}", True, (255, 255, 255))
        surface.blit(lod_text, (10, 70))
        
        # Draw camera position
        cam_text = font.render(f"Camera: ({self.camera_pos[0]:.1f}, {self.camera_pos[1]:.1f})", 
                             True, (255, 255, 255))
        surface.blit(cam_text, (10, 100))
        
        # Draw zoom level
        zoom_text = font.render(f"Zoom: {self.zoom_level:.2f}x", True, (255, 255, 255))
        surface.blit(zoom_text, (10, 130))
    
    def clear_cache(self) -> None:
        """Clear the chunk surface cache."""
        self.chunk_surfaces.clear()
    
    def update(self, dt: float) -> None:
        """Update the renderer."""
        # Update chunk visibility based on camera position
        view_rect = self.get_view_rect()
        self.chunk_manager.update_loaded_chunks(
            (self.camera_pos[0], self.camera_pos[1]),
            max(self.view_size) / self.zoom_level * 0.6  # Load chunks in a radius around the view
        )

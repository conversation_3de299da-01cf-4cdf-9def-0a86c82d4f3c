"""
Chunk Manager for Living World RPG

Handles loading, unloading, and rendering of world chunks with different levels of detail.
Implements view frustum culling and LOD rendering for optimal performance.
"""

import numpy as np
import os
import pickle
import time
from pathlib import Path
from typing import Dict, Tuple, Optional, List, Set
from dataclasses import dataclass
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ChunkData:
    """Container for chunk data at different LOD levels."""
    lod0: Optional[np.ndarray] = None  # Full detail
    lod1: Optional[np.ndarray] = None  # Medium detail
    lod2: Optional[np.ndarray] = None  # Low detail
    last_accessed: float = 0.0
    is_modified: bool = False

class ChunkManager:
    """Manages loading, caching, and rendering of world chunks."""

    def __init__(self, world_dir: str, chunk_size: int = 32, cache_size: int = 100):
        """
        Initialize the ChunkManager.

        Args:
            world_dir: Directory to store chunk data
            chunk_size: Size of each chunk in tiles
            cache_size: Maximum number of chunks to keep in memory
        """
        self.world_dir = Path(world_dir)
        self.chunk_size = chunk_size
        self.cache_size = cache_size
        self.chunk_cache: Dict[Tuple[int, int], ChunkData] = {}
        self.loaded_chunks: Set[Tuple[int, int]] = set()

        # Create world directory if it doesn't exist
        self.world_dir.mkdir(parents=True, exist_ok=True)

    def get_chunk_path(self, chunk_x: int, chunk_y: int) -> Path:
        """Get the file path for a chunk."""
        return self.world_dir / f"chunk_{chunk_x}_{chunk_y}.dat"

    def chunk_exists(self, chunk_x: int, chunk_y: int) -> bool:
        """Check if a chunk exists on disk."""
        return self.get_chunk_path(chunk_x, chunk_y).exists()

    def load_chunk(self, chunk_x: int, chunk_y: int) -> Optional[ChunkData]:
        """Load a chunk from disk or cache."""
        chunk_key = (chunk_x, chunk_y)

        # Return from cache if available
        if chunk_key in self.chunk_cache:
            self.chunk_cache[chunk_key].last_accessed = time.time()
            return self.chunk_cache[chunk_key]

        # Load from disk if it exists
        chunk_path = self.get_chunk_path(chunk_x, chunk_y)
        if chunk_path.exists():
            try:
                with open(chunk_path, 'rb') as f:
                    chunk_data = pickle.load(f)

                # Add to cache
                self._add_to_cache(chunk_x, chunk_y, chunk_data)
                return chunk_data

            except Exception as e:
                logger.error(f"Error loading chunk ({chunk_x}, {chunk_y}): {e}")

        return None

    def save_chunk(self, chunk_x: int, chunk_y: int, chunk_data: ChunkData) -> None:
        """Save a chunk to disk."""
        chunk_path = self.get_chunk_path(chunk_x, chunk_y)
        try:
            with open(chunk_path, 'wb') as f:
                pickle.dump(chunk_data, f)
            chunk_data.is_modified = False
        except Exception as e:
            logger.error(f"Error saving chunk ({chunk_x}, {chunk_y}): {e}")

    def _add_to_cache(self, chunk_x: int, chunk_y: int, chunk_data: ChunkData) -> None:
        """Add a chunk to the cache, evicting old chunks if necessary."""
        chunk_key = (chunk_x, chunk_y)

        # Evict old chunks if cache is full
        if len(self.chunk_cache) >= self.cache_size:
            # Find least recently used chunk
            lru_chunk = min(self.chunk_cache.items(), key=lambda x: x[1].last_accessed)
            lru_key, lru_data = lru_chunk

            # Save if modified
            if lru_data.is_modified:
                self.save_chunk(*lru_key, lru_data)

            # Remove from cache
            del self.chunk_cache[lru_key]

        # Add to cache
        chunk_data.last_accessed = time.time()
        self.chunk_cache[chunk_key] = chunk_data

    def get_chunk_lod(self, chunk_x: int, chunk_y: int, lod_level: int) -> Optional[np.ndarray]:
        """Get a chunk at the specified LOD level."""
        chunk_data = self.load_chunk(chunk_x, chunk_y)
        if not chunk_data:
            return None

        # Return the appropriate LOD level
        if lod_level == 0 and chunk_data.lod0 is not None:
            return chunk_data.lod0
        elif lod_level == 1 and chunk_data.lod1 is not None:
            return chunk_data.lod1
        elif lod_level == 2 and chunk_data.lod2 is not None:
            return chunk_data.lod2

        # If requested LOD doesn't exist, return the highest available LOD
        if chunk_data.lod0 is not None:
            return chunk_data.lod0
        elif chunk_data.lod1 is not None:
            return chunk_data.lod1
        elif chunk_data.lod2 is not None:
            return chunk_data.lod2

        return None

    def update_loaded_chunks(self, view_center: Tuple[float, float], view_radius: float) -> None:
        """
        Update which chunks are loaded based on the current view.

        Args:
            view_center: (x, y) center of the view in world coordinates
            view_radius: Radius around the view center to load chunks
        """
        # Convert to chunk coordinates
        chunk_radius = int(np.ceil(view_radius / self.chunk_size))
        center_chunk_x = int(view_center[0] // self.chunk_size)
        center_chunk_y = int(view_center[1] // self.chunk_size)

        # Determine which chunks should be loaded
        chunks_to_load = set()
        for dy in range(-chunk_radius, chunk_radius + 1):
            for dx in range(-chunk_radius, chunk_radius + 1):
                chunk_x = center_chunk_x + dx
                chunk_y = center_chunk_y + dy

                # Check if chunk is within view radius
                chunk_center = (
                    (chunk_x + 0.5) * self.chunk_size,
                    (chunk_y + 0.5) * self.chunk_size
                )
                distance = np.hypot(
                    chunk_center[0] - view_center[0],
                    chunk_center[1] - view_center[1]
                )

                if distance <= view_radius + (self.chunk_size * 1.5):
                    chunks_to_load.add((chunk_x, chunk_y))

        # Unload chunks that are too far away
        chunks_to_unload = self.loaded_chunks - chunks_to_load
        for chunk_x, chunk_y in chunks_to_unload:
            self.unload_chunk(chunk_x, chunk_y)

        # Load new chunks
        for chunk_x, chunk_y in chunks_to_load - self.loaded_chunks:
            self.load_chunk(chunk_x, chunk_y)

        self.loaded_chunks = chunks_to_load

    def unload_chunk(self, chunk_x: int, chunk_y: int) -> None:
        """Unload a chunk from memory."""
        chunk_key = (chunk_x, chunk_y)
        if chunk_key in self.chunk_cache:
            chunk_data = self.chunk_cache[chunk_key]
            if chunk_data.is_modified:
                self.save_chunk(chunk_x, chunk_y, chunk_data)
            del self.chunk_cache[chunk_key]

        if chunk_key in self.loaded_chunks:
            self.loaded_chunks.remove(chunk_key)

    def save_all_changes(self) -> None:
        """Save all modified chunks to disk."""
        for (chunk_x, chunk_y), chunk_data in self.chunk_cache.items():
            if chunk_data.is_modified:
                self.save_chunk(chunk_x, chunk_y, chunk_data)

    def get_visible_chunks(self, view_rect: Tuple[float, float, float, float],
                         lod_level: int = 0) -> List[Tuple[int, int, np.ndarray]]:
        """
        Get all chunks that are visible in the current view.

        Args:
            view_rect: (x, y, width, height) of the view rectangle in world coordinates
            lod_level: Level of detail to use (0=highest, 2=lowest)

        Returns:
            List of (chunk_x, chunk_y, chunk_data) tuples for visible chunks
        """
        visible_chunks = []
        view_x, view_y, view_w, view_h = view_rect

        # Convert view rectangle to chunk coordinates
        min_chunk_x = int(view_x // self.chunk_size)
        min_chunk_y = int(view_y // self.chunk_size)
        max_chunk_x = int((view_x + view_w) // self.chunk_size) + 1
        max_chunk_y = int((view_y + view_h) // self.chunk_size) + 1

        # Add a border of chunks around the view
        border = 1
        min_chunk_x = max(0, min_chunk_x - border)
        min_chunk_y = max(0, min_chunk_y - border)

        for chunk_y in range(min_chunk_y, max_chunk_y + border):
            for chunk_x in range(min_chunk_x, max_chunk_x + border):
                chunk_data = self.get_chunk_lod(chunk_x, chunk_y, lod_level)
                if chunk_data is not None:
                    visible_chunks.append((chunk_x, chunk_y, chunk_data))

        return visible_chunks

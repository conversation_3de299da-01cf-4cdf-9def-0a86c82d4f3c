# File: src/test_combat.py
"""
Combat Test Scenario for Living World RPG

This script sets up and runs various combat scenarios to test the combat system.
It creates different types of creatures and NPCs and simulates combat between them.
"""

import time
import random
import logging
import math
import sys
from typing import Dict, List, Optional, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("CombatTest")

# Add the parent directory to the path so we can import modules
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Create a mock implementation for testing purposes
class Entity:
    def __init__(self):
        self.id = 1
        self.components = {}

    def add_component(self, component):
        component_type = type(component)
        self.components[component_type] = component

    def get_component(self, component_type):
        return self.components.get(component_type)

class EntityManager:
    def __init__(self):
        self.entities = {}

    def create_entity(self):
        entity = Entity()
        self.entities[entity.id] = entity
        return entity

from core.game_state import GameState


class PositionComponent:
    def __init__(self, x, y):
        self.x = x
        self.y = y

class CombatComponent:
    def __init__(self, attack=10, defense=5, health=100, stamina=100):
        self.attack = attack
        self.defense = defense
        self.health = health
        self.max_health = health
        self.stamina = stamina
        self.max_stamina = stamina
        self.in_combat = False
        self.status_effects = {}

    def is_alive(self):
        return self.health > 0

class WeaponComponent:
    def __init__(self, weapon_type="sword", base_damage=10, damage_type="physical",
                 attack_speed=1.0, range=1.0, critical_bonus=0.0, special_effects=None):
        self.weapon_type = weapon_type
        self.base_damage = base_damage
        self.damage_type = damage_type
        self.attack_speed = attack_speed
        self.range = range
        self.critical_bonus = critical_bonus
        self.special_effects = special_effects or {}

class ArmorComponent:
    def __init__(self, armor_type="light", defense_bonus=5, resistances=None, weight=1.0, slot=None):
        self.armor_type = armor_type
        self.defense_bonus = defense_bonus
        self.resistances = resistances or {}
        self.weight = weight
        self.slot = slot

class CombatSystem:
    def __init__(self):
        self.game_state = None
        self._combat_log = []

    def start_combat(self, entity1_id, entity2_id, entities):
        self._combat_log.append(f"Combat started between {entity1_id} and {entity2_id}")

    def update(self, time_step, entities):
        self._combat_log.append(f"Combat updated with time step {time_step}")

    def get_combat_log(self):
        log = self._combat_log.copy()
        self._combat_log = []
        return log

class AISystem:
    def __init__(self):
        self.game_state = None

    def update(self, time_step, entities):
        pass

class CreatureFactory:
    def __init__(self, entity_manager):
        self.entity_manager = entity_manager

    def create_creature(self, creature_type, x, y, **kwargs):
        entity = self.entity_manager.create_entity()
        entity.add_component(PositionComponent(x, y))

        # Add creature component
        creature_comp = CreatureComponent(creature_type.capitalize(), "hostile", 20, 20)
        entity.add_component(creature_comp)

        # Add combat component
        combat_comp = CombatComponent()
        entity.add_component(combat_comp)

        # Add weapon component
        weapon_comp = WeaponComponent()
        entity.add_component(weapon_comp)

        return entity

class CreatureComponent:
    def __init__(self, creature_name, behavior, hit_points, max_hit_points):
        self.creature_name = creature_name
        self.behavior = behavior
        self.hit_points = hit_points
        self.max_hit_points = max_hit_points


class CombatTestScenario:
    """
    Class for setting up and running combat test scenarios.
    """
    def __init__(self):
        self.entity_manager = EntityManager()
        self.game_state = GameState()
        self.combat_system = CombatSystem()
        self.ai_system = AISystem()
        self.creature_factory = CreatureFactory(self.entity_manager)

        # Connect systems to game state
        self.combat_system.game_state = self.game_state
        self.ai_system.game_state = self.game_state
        self.game_state.combat_system = self.combat_system

    def setup_1v1_scenario(self, creature1_type: str, creature2_type: str) -> None:
        """
        Set up a 1v1 combat scenario between two creatures.

        Args:
            creature1_type: Type of the first creature
            creature2_type: Type of the second creature
        """
        logger.info(f"Setting up 1v1 scenario: {creature1_type} vs {creature2_type}")

        # Create the creatures
        creature1 = self.creature_factory.create_creature(creature1_type, 0, 0)
        creature2 = self.creature_factory.create_creature(creature2_type, 2, 0)  # 2 units away

        # Log creature stats
        self._log_creature_stats(creature1)
        self._log_creature_stats(creature2)

        # Start combat
        self.combat_system.start_combat(creature1.id, creature2.id, self.entity_manager.entities)

    def setup_group_scenario(self, group1_types: List[str], group2_types: List[str]) -> None:
        """
        Set up a group combat scenario between two groups of creatures.

        Args:
            group1_types: List of creature types for group 1
            group2_types: List of creature types for group 2
        """
        logger.info(f"Setting up group scenario: {group1_types} vs {group2_types}")

        # Create group 1
        group1_entities = []
        for i, creature_type in enumerate(group1_types):
            x = -5 + (i * 2)  # Space them out
            y = random.uniform(-2, 2)
            creature = self.creature_factory.create_creature(creature_type, x, y)
            group1_entities.append(creature)
            self._log_creature_stats(creature)

        # Create group 2
        group2_entities = []
        for i, creature_type in enumerate(group2_types):
            x = 5 + (i * 2)  # Space them out on the other side
            y = random.uniform(-2, 2)
            creature = self.creature_factory.create_creature(creature_type, x, y)
            group2_entities.append(creature)
            self._log_creature_stats(creature)

        # Start combat between the groups
        for creature1 in group1_entities:
            # Find the nearest enemy
            nearest_enemy = None
            nearest_distance = float('inf')

            for creature2 in group2_entities:
                pos1 = creature1.get_component(PositionComponent)
                pos2 = creature2.get_component(PositionComponent)

                if pos1 and pos2:
                    distance = ((pos1.x - pos2.x) ** 2 + (pos1.y - pos2.y) ** 2) ** 0.5
                    if distance < nearest_distance:
                        nearest_distance = distance
                        nearest_enemy = creature2

            if nearest_enemy:
                self.combat_system.start_combat(creature1.id, nearest_enemy.id, self.entity_manager.entities)

    def setup_ambush_scenario(self, target_type: str, ambusher_types: List[str]) -> None:
        """
        Set up an ambush scenario where multiple creatures ambush a single target.

        Args:
            target_type: Type of the target creature
            ambusher_types: List of ambusher creature types
        """
        logger.info(f"Setting up ambush scenario: {ambusher_types} ambushing {target_type}")

        # Create the target
        target = self.creature_factory.create_creature(target_type, 0, 0)
        self._log_creature_stats(target)

        # Create the ambushers in a circle around the target
        ambushers = []
        for i, ambusher_type in enumerate(ambusher_types):
            angle = 2 * 3.14159 * i / len(ambusher_types)
            x = 3 * math.cos(angle)  # 3 units away in a circle
            y = 3 * math.sin(angle)
            ambusher = self.creature_factory.create_creature(ambusher_type, x, y)
            ambushers.append(ambusher)
            self._log_creature_stats(ambusher)

        # Start combat
        for ambusher in ambushers:
            self.combat_system.start_combat(ambusher.id, target.id, self.entity_manager.entities)

    def run_simulation(self, duration: float, time_step: float = 0.1) -> None:
        """
        Run the combat simulation for a specified duration.

        Args:
            duration: Duration of the simulation in seconds
            time_step: Time step for each update
        """
        logger.info(f"Starting combat simulation for {duration} seconds")

        elapsed_time = 0
        while elapsed_time < duration:
            # Update systems
            self.combat_system.update(time_step, self.entity_manager.entities)
            self.ai_system.update(time_step, self.entity_manager.entities)

            # Log combat events
            for event in self.combat_system.get_combat_log():
                logger.info(event)

            # Check if combat is over (all entities on one side are dead)
            if self._is_combat_over():
                logger.info("Combat has ended")
                break

            # Increment time
            elapsed_time += time_step
            time.sleep(0.05)  # Slow down the simulation for readability

        logger.info(f"Simulation ended after {elapsed_time:.1f} seconds")
        self._log_final_results()

    def _is_combat_over(self) -> bool:
        """
        Check if the combat is over (all entities on one side are dead).

        Returns:
            True if combat is over, False otherwise
        """
        # Count alive entities
        alive_count = 0
        for entity_id, entity in self.entity_manager.entities.items():
            combat_comp = entity.get_component(CombatComponent)
            if combat_comp and combat_comp.is_alive():
                alive_count += 1

        # If only 0 or 1 entities are alive, combat is over
        return alive_count <= 1

    def _log_creature_stats(self, creature: Entity) -> None:
        """
        Log the stats of a creature.

        Args:
            creature: The creature entity
        """
        combat_comp = creature.get_component(CombatComponent)
        weapon_comp = creature.get_component(WeaponComponent)
        armor_comp = creature.get_component(ArmorComponent)

        if not combat_comp:
            return

        logger.info(f"Creature: {self._get_entity_name(creature)}")
        logger.info(f"  Health: {combat_comp.health}/{combat_comp.max_health}")
        logger.info(f"  Attack: {combat_comp.attack}")
        logger.info(f"  Defense: {combat_comp.defense}")

        if weapon_comp:
            logger.info(f"  Weapon: {weapon_comp.weapon_type} (Damage: {weapon_comp.base_damage})")

        if armor_comp:
            logger.info(f"  Armor: {armor_comp.armor_type} (Defense: {armor_comp.defense_bonus})")

    def _log_final_results(self) -> None:
        """
        Log the final results of the combat.
        """
        logger.info("Final Results:")

        for entity_id, entity in self.entity_manager.entities.items():
            combat_comp = entity.get_component(CombatComponent)
            if not combat_comp:
                continue

            status = "Alive" if combat_comp.is_alive() else "Dead"
            logger.info(f"{self._get_entity_name(entity)}: {status} ({combat_comp.health}/{combat_comp.max_health} HP)")

    def _get_entity_name(self, entity: Entity) -> str:
        """
        Get a display name for an entity.

        Args:
            entity: The entity

        Returns:
            Display name for the entity
        """
        # Try different components that might have a name
        player_comp = entity.get_component("PlayerComponent")
        if player_comp and hasattr(player_comp, "name"):
            return player_comp.name

        creature_comp = entity.get_component("CreatureComponent")
        if creature_comp and hasattr(creature_comp, "creature_name"):
            return creature_comp.creature_name

        # Fallback to entity ID
        return f"Entity {entity.id}"


def main():
    """
    Main function to run the combat test scenarios.
    """
    logger.info("Starting Combat Test Scenarios")

    # Create the test scenario
    test = CombatTestScenario()

    # Run different scenarios
    logger.info("\n\n=== 1v1 Combat Test: Wolf vs Bandit ===\n")
    test.setup_1v1_scenario("wolf", "bandit")
    test.run_simulation(30)  # Run for 30 seconds

    # Create a new test for each scenario
    test = CombatTestScenario()
    logger.info("\n\n=== Group Combat Test: Wolves vs Guards ===\n")
    test.setup_group_scenario(["wolf", "wolf", "wolf"], ["guard", "guard"])
    test.run_simulation(60)  # Run for 60 seconds

    test = CombatTestScenario()
    logger.info("\n\n=== Ambush Test: Bear ambushed by Bandits ===\n")
    test.setup_ambush_scenario("bear", ["bandit", "bandit", "bandit", "bandit"])
    test.run_simulation(60)  # Run for 60 seconds

    logger.info("All combat tests completed")


if __name__ == "__main__":
    main()

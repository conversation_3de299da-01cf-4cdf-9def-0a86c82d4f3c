# File: tests/test_performance_optimizations.py
"""
Comprehensive tests for performance optimizations across the codebase.
Tests ECS improvements, UI caching, data loading enhancements, and memory management.
"""

import pytest
import time
import tempfile
import os
import json
from unittest.mock import Mock, patch

# Import the optimized modules
from src.core.ecs import ECSWorld, Entity
from src.core.components import PositionComponent, PlayerComponent
from src.ui_util import draw_text, get_cache_stats, clear_ui_caches
from src.data_loader import DataLoader
from src.world.world_manager import WorldManager


class TestECSOptimizations:
    """Test ECS performance improvements."""
    
    def test_entity_pooling(self):
        """Test that entity pooling works correctly."""
        world = ECSWorld()
        
        # Create and destroy entities to populate the pool
        entities = []
        for i in range(10):
            entity = world.create_entity()
            entity.add_component(PositionComponent(i, i))
            entities.append(entity)
        
        # Destroy half the entities
        for entity in entities[:5]:
            world.destroy_entity(entity)
        
        # Check that pool has entities
        assert len(world._entity_pool) == 5
        
        # Create new entities - should reuse from pool
        new_entity = world.create_entity()
        assert len(world._entity_pool) == 4  # One less in pool
        assert len(world.entities) == 6  # 5 remaining + 1 new
    
    def test_component_indexing(self):
        """Test that component indexing improves query performance."""
        world = ECSWorld()
        
        # Create entities with different components
        for i in range(100):
            entity = world.create_entity()
            entity.add_component(PositionComponent(i, i))
            if i % 2 == 0:
                entity.add_component(PlayerComponent("Player"))
        
        # Test indexed query performance
        start_time = time.time()
        entities_with_position = world.get_entities_with_component(PositionComponent)
        query_time = time.time() - start_time
        
        assert len(entities_with_position) == 100
        assert query_time < 0.01  # Should be very fast with indexing
    
    def test_query_caching(self):
        """Test that query results are cached for performance."""
        world = ECSWorld()
        
        # Create test entities
        for i in range(50):
            entity = world.create_entity()
            entity.add_component(PositionComponent(i, i))
        
        # First query - should cache result
        start_time = time.time()
        result1 = world.get_entities_with(PositionComponent)
        first_query_time = time.time() - start_time
        
        # Second identical query - should use cache
        start_time = time.time()
        result2 = world.get_entities_with(PositionComponent)
        second_query_time = time.time() - start_time
        
        assert len(result1) == len(result2) == 50
        assert second_query_time < first_query_time  # Cache should be faster
    
    def test_system_priorities(self):
        """Test that systems run in priority order."""
        world = ECSWorld()
        execution_order = []
        
        class TestSystem1:
            def __init__(self, name):
                self.name = name
                self.world = None
            def required_components(self):
                return []
            def register_entity(self, entity):
                pass
            def unregister_entity(self, entity):
                pass
            def update(self, dt, **kwargs):
                execution_order.append(self.name)
        
        # Add systems with different priorities
        world.add_system(TestSystem1("Low"), priority=1)
        world.add_system(TestSystem1("High"), priority=10)
        world.add_system(TestSystem1("Medium"), priority=5)
        
        # Update world
        world.update(0.016)
        
        # Check execution order (higher priority first)
        assert execution_order == ["High", "Medium", "Low"]


class TestUIOptimizations:
    """Test UI caching and performance improvements."""
    
    def test_text_caching(self):
        """Test that text rendering is cached."""
        clear_ui_caches()
        
        # Mock pygame font and screen
        with patch('src.ui_util.pygame') as mock_pygame:
            mock_font = Mock()
            mock_font.render.return_value = Mock()
            mock_font.get_height.return_value = 20
            mock_screen = Mock()
            
            # First render - should call font.render
            draw_text(mock_screen, mock_font, "Test Text", 10, 10)
            assert mock_font.render.call_count == 1
            
            # Second render of same text - should use cache
            draw_text(mock_screen, mock_font, "Test Text", 10, 10)
            assert mock_font.render.call_count == 1  # No additional calls
            
            # Check cache stats
            stats = get_cache_stats()
            assert stats['cache_hits'] == 1
            assert stats['cache_misses'] == 1
    
    def test_cache_performance(self):
        """Test that caching improves performance."""
        clear_ui_caches()
        
        with patch('src.ui_util.pygame') as mock_pygame:
            mock_font = Mock()
            mock_font.render.return_value = Mock()
            mock_font.get_height.return_value = 20
            mock_screen = Mock()
            
            # Measure time for multiple renders
            start_time = time.time()
            for i in range(100):
                draw_text(mock_screen, mock_font, "Cached Text", 10, 10)
            cached_time = time.time() - start_time
            
            # Should be very fast due to caching
            assert cached_time < 0.1


class TestDataLoaderOptimizations:
    """Test data loader caching and validation."""
    
    def test_file_caching(self):
        """Test that file loading is cached."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create test data file
            test_data = {"test": "data", "items": {"sword": {"type": "weapon"}}}
            test_file = os.path.join(temp_dir, "test.json")
            with open(test_file, 'w') as f:
                json.dump(test_data, f)
            
            loader = DataLoader(temp_dir, enable_caching=True)
            
            # First load
            start_time = time.time()
            data1 = loader.load_data("test.json")
            first_load_time = time.time() - start_time
            
            # Second load - should use cache
            start_time = time.time()
            data2 = loader.load_data("test.json")
            second_load_time = time.time() - start_time
            
            assert data1 == data2 == test_data
            assert second_load_time < first_load_time
            
            # Check performance stats
            stats = loader.get_performance_stats()
            assert stats['cache_hits'] == 1
            assert stats['cache_misses'] == 1
    
    def test_data_validation(self):
        """Test that data validation works correctly."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create invalid biome data
            invalid_data = {"Invalid Biome": "not a dict"}
            test_file = os.path.join(temp_dir, "biomes.json")
            with open(test_file, 'w') as f:
                json.dump(invalid_data, f)
            
            loader = DataLoader(temp_dir, enable_validation=True)
            data = loader.load_data("biomes.json")
            
            # Should still load but have validation errors
            assert data == invalid_data
            errors = loader.get_validation_errors()
            assert "biomes.json" in errors
            assert len(errors["biomes.json"]) > 0
    
    def test_hot_reload(self):
        """Test that file changes are detected and reloaded."""
        with tempfile.TemporaryDirectory() as temp_dir:
            test_file = os.path.join(temp_dir, "test.json")
            
            # Create initial file
            initial_data = {"version": 1}
            with open(test_file, 'w') as f:
                json.dump(initial_data, f)
            
            loader = DataLoader(temp_dir, enable_caching=True)
            data1 = loader.load_data("test.json")
            assert data1 == initial_data
            
            # Modify file
            time.sleep(0.1)  # Ensure different timestamp
            updated_data = {"version": 2}
            with open(test_file, 'w') as f:
                json.dump(updated_data, f)
            
            # Check if change is detected
            assert loader.has_file_changed("test.json")
            
            # Reload should get new data
            data2 = loader.reload_file("test.json")
            assert data2 == updated_data


class TestMemoryOptimizations:
    """Test memory management improvements."""
    
    def test_world_manager_memory_limits(self):
        """Test that world manager respects memory limits."""
        # This would require more complex setup with actual world data
        # For now, test the memory tracking methods exist
        with patch('src.world.world_manager.TerrainGenerator'):
            with patch('src.world.world_manager.BiomeManager'):
                with patch('src.world.world_manager.EventManager'):
                    with patch('src.world.world_manager.EconomyManager'):
                        with patch('src.world.world_manager.FactionManager'):
                            # Create world manager with low memory limits
                            world_manager = WorldManager({}, None)
                            world_manager.max_active_regions = 2
                            
                            # Test memory stats method exists
                            stats = world_manager.get_memory_stats()
                            assert 'active_regions' in stats
                            assert 'max_regions' in stats
                            assert 'estimated_memory_mb' in stats


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
